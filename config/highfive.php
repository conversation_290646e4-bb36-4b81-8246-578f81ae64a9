<?php

declare(strict_types=1);

use App\Enums\ExpenseCategory;
use App\Enums\VendorType;

return [
    'nightwatch_custom_rate' => env('HIGHFIVE_NIGHTWATCH_CUSTOM_RATE', 0.05),

    'place_order_bot' => [
        'base_uri' => env('HIGHFIVE_PLACE_ORDER_BOT_URL', 'http://3.94.19.0:1719/ENVIRONMENT'),
        'username' => env('HIGHFIVE_PLACE_ORDER_BOT_USERNAME', ''),
        'password' => env('HIGHFIVE_PLACE_ORDER_BOT_PASSWORD', ''),
        'webhook_secret' => env('HIGHFIVE_PLACE_ORDER_BOT_WEBHOOK_SECRET', 'highfive-2025-aaaaaaaaaaaaaaa123'),
    ],
    'orders' => [
        'bcc_email' => '<EMAIL>',
    ],

    'mwi' => [
        'base_uri' => env('HIGHFIVE_MWI_URL', 'https://betaws.mwiah.com/PartnerServices/4.0/'),
        'api_access_request_email' => env('HIGHFIVE_MWI_API_ACCESS_REQUEST_EMAIL'),
        'api_access_request_name' => env('HIGHFIVE_MWI_API_ACCESS_REQUEST_NAME', 'MWI Animal Health'),
        'api_access_request_email_bcc' => '<EMAIL>',
    ],

    'amazon' => [
        'base_uri' => env('HIGHFIVE_AMAZON_URL', 'https://api.amazon.com/'),
        'client_id' => env('HIGHFIVE_AMAZON_CLIENT_ID', ''),
        'client_secret' => env('HIGHFIVE_AMAZON_CLIENT_SECRET', ''),
    ],

    'patterson' => [
        'base_uri' => env('PATTERSON_BASE_URI', 'https://editest.pattersoncompanies.com/HttpAdapter/HttpMessageServlet?interfaceNamespace=http://PCO.com/Customer&interface=SI_OB_OrdReq&senderService=BC_HighFiveVet&senderParty=Customer&qos=BE'),
        'username' => env('PATTERSON_USERNAME', ''),
        'password' => env('PATTERSON_PASSWORD', ''),
    ],

    'gervetusa' => [
        'base_uri' => env('GERVETUSA_BASE_URI', 'https://www.gervetusa.com'),
    ],

    'catalog_sync' => [
        'service_endpoint' => env('HIGHFIVE_CATALOG_SYNC_SERVICE_ENDPOINT', 'http://localhost:3000'),
        'results_queue_region' => env('HIGHFIVE_CATALOG_SYNC_RESULTS_QUEUE_REGION', 'us-east-1'),
        'results_queue_url' => env('HIGHFIVE_CATALOG_SYNC_RESULTS_QUEUE_URL', ''),
        'schedule_interval' => 7,
    ],

    'vendors' => [
        [
            'key' => 'PATN',
            'name' => 'Patterson',
            'image_path' => 'vendor-images/patterson.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => '************',
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '5PM Local Time',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 1199,
            ],
        ],
        [
            'key' => 'MWIX',
            'name' => 'MWI',
            'image_path' => 'vendor-images/mwi.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [
                ['name' => null, 'email' => '<EMAIL>'],
            ],
            'shipping_terms' => [
                'cutoff_time' => '5PM Local Time',
                'free_shipping_threshold' => 15000,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'COVR',
            'name' => 'Covetrus',
            'image_path' => 'vendor-images/covetrus.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => '************',
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '5PM Local Time',
                'free_shipping_threshold' => 25000,
                'shipping_rate' => 1200,
            ],
        ],
        [
            'key' => 'MWST',
            'name' => 'Midwest',
            'image_path' => 'vendor-images/midwest.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [
                ['name' => null, 'email' => '<EMAIL>'],
                ['name' => null, 'email' => '<EMAIL>'],
                ['name' => null, 'email' => '<EMAIL>'],
                ['name' => null, 'email' => '<EMAIL>'],
            ],
            'shipping_terms' => [
                'cutoff_time' => '5PM CST',
                'free_shipping_threshold' => 15000,
                'shipping_rate' => 1000,
            ],
        ],
        [
            'key' => 'FIVT',
            'name' => 'First Vet',
            'image_path' => 'vendor-images/firstvet.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 10000,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'PNVT',
            'name' => 'PennVet',
            'image_path' => 'vendor-images/penn-vet.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => '************',
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '7:30PM EST',
                'free_shipping_threshold' => 17500,
                'shipping_rate' => 1500,
            ],
        ],
        [
            'key' => 'VIMG',
            'name' => 'Victor Medical Group',
            'image_path' => 'vendor-images/victor-medical-group.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 10000,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'AMTN',
            'name' => 'Amatheon',
            'image_path' => 'vendor-images/amatheon.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 10000,
                'shipping_rate' => 995,
            ],
        ],
        [
            'key' => 'PHAH',
            'name' => 'Pharmsource AH',
            'image_path' => 'vendor-images/pharmasource.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => '************',
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 20000,
                'shipping_rate' => 2750,
            ],
        ],
        [
            'key' => 'MDVT',
            'name' => 'MedVet',
            'image_path' => 'vendor-images/medvet.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 30000,
                'shipping_rate' => 1000,
            ],
        ],
        [
            'key' => 'NEAH',
            'name' => 'New England Animal Health',
            'image_path' => 'vendor-images/new-england-animal-health.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 10000,
                'shipping_rate' => 1000,
            ],
        ],
        [
            'key' => 'ZOET',
            'name' => 'Zoetis',
            'image_path' => 'vendor-images/zoetis.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '2PM PST',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'BOIH',
            'name' => 'Boehringer Ingelheim',
            'image_path' => 'vendor-images/bi.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '5PM EST',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'ELAH',
            'name' => 'Elanco Animal Health',
            'image_path' => 'vendor-images/elanco-animal-health.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'HPNU',
            'name' => "Hill's Pet Nutrition",
            'image_path' => 'vendor-images/hills-pet-nutrition.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '1PM Local Time',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'PRNA',
            'name' => 'Purina',
            'image_path' => 'vendor-images/purina.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'RYCN',
            'name' => 'Royal Canin',
            'image_path' => 'vendor-images/royal-canin.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'MDLN',
            'name' => 'Medline',
            'image_path' => 'vendor-images/medline.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'WDPH',
            'name' => 'Wedgewood Pharmacy',
            'image_path' => 'vendor-images/wedgewood-pharmacy.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '10:30AM EST',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'PESC',
            'name' => 'Pet Script',
            'image_path' => 'vendor-images/pet-script.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'RDRN',
            'name' => 'Roadrunner',
            'image_path' => 'vendor-images/roadrunner.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'TYPH',
            'name' => 'Taylors Pharmacy',
            'image_path' => 'vendor-images/taylors-pharmacy.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'MIXL',
            'name' => 'Mixlab',
            'image_path' => 'vendor-images/mixlab.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'SVPH',
            'name' => 'Specialty Veterinary Pharmacy',
            'image_path' => 'vendor-images/specialty-veterinary-pharmacy.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'IDEX',
            'name' => 'Idexx',
            'image_path' => 'vendor-images/idexx.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '6PM EST',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'ZODI',
            'name' => 'Zoetis Diagnostics',
            'image_path' => 'vendor-images/zoetis-diagnostics.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => '<EMAIL>',
            'new_account_email' => '<EMAIL>',
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => '2PM PST',
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'ANTC',
            'name' => 'Antech',
            'image_path' => 'vendor-images/antech.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => [
                'cutoff_time' => null,
                'free_shipping_threshold' => 0,
                'shipping_rate' => 0,
            ],
        ],
        [
            'key' => 'ZOMD',
            'name' => 'Zomedica',
            'image_path' => 'vendor-images/zomedica.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'MOCH',
            'name' => 'Moichor',
            'image_path' => 'vendor-images/moichor.png',
            'type' => VendorType::Manufacturer,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'AMZN',
            'name' => 'Amazon',
            'image_path' => 'vendor-images/amazon.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::GA,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'COST',
            'name' => 'Costco',
            'image_path' => 'vendor-images/costco.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::GA,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'GERVET',
            'name' => 'GerVetUSA',
            'image_path' => 'vendor-images/gervetusa.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => true,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'ANSP',
            'name' => 'Animal Supply',
            'image_path' => 'vendor-images/animal-supply.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'VTCO',
            'name' => 'Vetcove',
            'image_path' => 'vendor-images/vetcove.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'VRSX',
            'name' => 'VRS',
            'image_path' => 'vendor-images/vrs.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'STPR',
            'name' => 'Standard Process',
            'image_path' => 'vendor-images/standard-process.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'RGRT',
            'name' => 'Right Ratio',
            'image_path' => 'vendor-images/right-ratio.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'LHOM',
            'name' => 'Lhasa Oms',
            'image_path' => 'vendor-images/lhasa-oms.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
        [
            'key' => 'DXHT',
            'name' => 'Dr. Xie Jing Tang Herbal Store',
            'image_path' => 'vendor-images/dr-xie-jing-tang.png',
            'type' => VendorType::Distributor,
            'expense_category' => ExpenseCategory::COGS,
            'purchase_order_email' => null,
            'new_account_email' => null,
            'phone_number' => null,
            'credentials_schema' => null,
            'is_enabled' => false,
            'account_receivable_contacts' => [],
            'shipping_terms' => null,
        ],
    ],
];
