<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reassign_swap_set_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('file_path');
            $table->unsignedInteger('total_rows_count')->default(0);
            $table->unsignedInteger('processed_rows_count')->default(0);
            $table->unsignedInteger('skipped_or_failed_rows_count')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('finished_at')->nullable();
            $table->timestamps();
        });
    }
};
