<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::transaction(function () {
            DB::statement("
                INSERT INTO gpo_accounts (id, name, created_at, updated_at)
                SELECT id, name, created_at, updated_at
                FROM accounts
                WHERE type = 'App\Modules\Account\Models\GpoAccount'
            ");

            DB::statement("
                UPDATE promotions
                SET promotionable_type = 'App\Modules\Gpo\Models\GpoAccount'
                WHERE promotionable_type = 'App\Modules\Account\Models\GpoAccount'
            ");

            Schema::table('accounts', function (Blueprint $table) {
                $table->dropForeign(['gpo_account_id']);
            });

            Schema::table('accounts', function (Blueprint $table) {
                $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
            });

            Schema::table('gpo_account_details', function (Blueprint $table) {
                $table->dropForeign(['gpo_account_id']);
            });

            Schema::table('gpo_account_details', function (Blueprint $table) {
                $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
            });

            Schema::table('gpo_recommended_products', function (Blueprint $table) {
                $table->dropForeign(['gpo_account_id']);
            });

            Schema::table('gpo_recommended_products', function (Blueprint $table) {
                $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
            });

            Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
                $table->dropForeign(['gpo_account_id']);
            });

            Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
                $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
            });

            Schema::table('gpo_invitations', function (Blueprint $table) {
                $table->dropForeign(['gpo_account_id']);
            });

            Schema::table('gpo_invitations', function (Blueprint $table) {
                $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
            });
        });
    }
};
