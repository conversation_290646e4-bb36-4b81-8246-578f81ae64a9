<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('gpo_account_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('gpo_account_id');
            $table->string('setting_key');
            $table->json('value');
            $table->timestamps();

            // Add foreign key constraint
            $table->foreign('gpo_account_id')
                ->references('id')
                ->on('gpo_accounts')
                ->onDelete('cascade');

            // Add unique constraint to prevent duplicate settings for the same account
            $table->unique(['gpo_account_id', 'setting_key']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('gpo_account_settings');
    }
};
