<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('monite_webhook_events', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('event_id')->unique()->comment('Unique ID of the event from Monite');
            $table->string('event_type')->index()->comment('Type of event (e.g., payable.created)');
            $table->string('entity_id')->index()->comment('Monite entity ID related to the event');
            $table->string('object_type')->nullable()->comment('Type of object affected (e.g., payable)');
            $table->string('object_id')->nullable()->index()->comment('ID of the object affected');
            $table->json('payload')->comment('Full webhook payload');
            $table->string('status')->default('pending')->index()->comment('Processing status');
            $table->text('error_message')->nullable()->comment('Error message if processing failed');
            $table->timestamp('processed_at')->nullable()->comment('When the event was processed');
            $table->timestamps();
        });
    }
};
