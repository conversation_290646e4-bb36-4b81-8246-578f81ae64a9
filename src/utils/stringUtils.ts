interface PriceConfig {
  currency?: string;
  formatter: Intl.NumberFormat;
}

const CURRENCY = {
  USD: 'USD',
};

const defaultPriceConfig = {
  currency: CURRENCY.USD,
  formatter: new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: CURRENCY.USD,
  }),
};

export const getPriceString = (
  price: number | string | null = 0,
  config: PriceConfig = defaultPriceConfig,
): string => {
  const { currency, formatter } = config;

  switch (currency) {
    case CURRENCY.USD:
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: CURRENCY.USD,
      }).format(Number(price));
    default:
      return formatter.format(Number(price));
  }
};

export const formatCompactNumber = (value: number | string): string => {
  const num = Number(value);

  if (num >= 1e12) {
    return `${(num / 1e12).toFixed(1)}T`;
  }
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(1)}B`;
  }
  if (num >= 1e6) {
    return `${(num / 1e6).toFixed(1)}M`;
  }
  if (num >= 1e3) {
    return `${(num / 1e3).toFixed(1)}K`;
  }

  return `~${(num / 1e3).toFixed(1)}K`;
};
