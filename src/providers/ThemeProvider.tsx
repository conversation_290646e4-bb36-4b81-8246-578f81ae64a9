import {
  Button,
  createTheme,
  DEFAULT_THEME,
  mergeMantineTheme,
  NumberInput,
  Paper,
  PasswordInput,
  Radio,
  Select,
  TextInput,
  ActionIcon,
  MantineProvider,
  Menu,
} from '@mantine/core';
import { DateInput } from '@mantine/dates';

import {
  getButtonVariables,
  getInputVariables,
  getNumberInputVariables,
  getRadioVariables,
  getSelectVariables,
  getActionIconButtonVariables,
} from '@/utils';
import { ReactNode } from 'react';

const themeOverride = createTheme({
  cursorType: 'pointer',
  primaryColor: 'yellow',
  colors: {
    yellow: [
      // TODO update colors
      '#FEFBEF',
      '#FEFBEF',
      '#FEFBEF',
      '#FDEFC1',
      '#FBE7A1',
      '#FADF82',
      '#F9D763',
      '#D1B450',
      '#A9913C',
      '#826D29',
    ],
    blue: [
      '#0032A0',
      '#d0dcfb',
      '#aac0fe',
      '#a3b9f8',
      '#728fea',
      '#3652ba',
      '#1b3bbb',
      '#24388a',
      '#111c44',
      '#0b1437',
      '#00184E',
      '#001544',
    ],
    green: [
      '#2B7113',
      '#B8E6A8',
      '#A9D998',
      '#89BF77',
      '#6AA555',
      '#4A8B34',
      '#2B7113',
      '#225A0F',
      '#1A440B',
      '#112D08',
      '#091704',
      '#CEEDC2',
    ],
    red: [
      '#A31838',
      '#FAD6CF',
      '#F5A6A1',
      '#E36F73',
      '#C74859',
      '#A31838',
      '#8C1139',
      '#750C38',
      '#5E0734',
      '#4E0431',
    ],
    dark: [
      '#98A2B3',
      '#F9FAFB',
      '#F2F4F7',
      '#EAECF0',
      '#D0D5DD',
      '#98A2B3',
      '#667085',
      '#555F74',
      '#344054',
      '#222222',
    ],
    'light-blue': [
      '#BDDCF0',
      '#F8FCFE',
      '#F2F8FC',
      '#E5F1F9',
      '#D7EAF6',
      '#CAE3F3',
      '#BDDCF0',
      '#9CBFD6',
      '#7BA2BC',
      '#5B86A1',
      '#3A6987',
    ],
    pink: [
      '#F1D9E6',
      '#FEFBFD',
      '#FCF7FA',
      '#F9F0F5',
      '#F7E8F0',
      '#F4E1EB',
      '#F1D9E6',
      '#D9B6C9',
      '#C094AC',
      '#A8718E',
      '#8F4F71',
    ],
    orange: [
      '#ED7F02',
      '#FEF0CB',
      '#FDDC98',
      '#F9C265',
      '#F4A73E',
      '#ED7F02',
      '#CB6301',
      '#AA4B01',
      '#893600',
      '#712700',
    ],
    turquoise: [
      '#57ABB0',
      '#B6F5F9',
      '#A1DBDE',
      '#8FCED2',
      '#7DC2C6',
      '#57ABB0',
      '#137980',
      '#0F6E75',
      '#0B494C',
      '#062426',
    ],
  },
  fontFamily: 'var(--font-sans)',
  fontSizes: {
    xxl: 'calc(2.5rem * var(--mantine-scale))',
    xl: 'calc(2rem * var(--mantine-scale))',
    xlLg: 'calc(1.5rem * var(--mantine-scale))',
    lg: 'calc(1.25rem * var(--mantine-scale))',
    lgMd: 'calc(1.125rem * var(--mantine-scale))',
    md: 'calc(1rem * var(--mantine-scale))',
    sm: 'calc(0.875rem * var(--mantine-scale))',
    xs: 'calc(0.75rem * var(--mantine-scale))',
  },
  lineHeights: {
    xxl: '1.7',
    xl: '1.6',
    xlLg: '1.5',
    lg: '1.4',
    lgMd: '1.3',
    md: '1.2',
    sm: '1.2',
    xs: '1.2',
    xxs: '1',
  },
  components: {
    TextInput: TextInput.extend({
      defaultProps: {
        radius: 'md',
        size: 'xs',
      },
      vars: getInputVariables,
    }),
    PasswordInput: PasswordInput.extend({
      defaultProps: {
        radius: 'md',
        size: 'xs',
      },
    }),
    Select: Select.extend({
      defaultProps: {
        radius: 'md',
        size: 'xs',
      },
      vars: getSelectVariables,
    }),
    NumberInput: NumberInput.extend({
      defaultProps: {
        radius: 'md',
        size: 'xs',
      },
      vars: getNumberInputVariables,
    }),
    Radio: Radio.extend({
      defaultProps: {
        size: 'xs',
      },
      vars: getRadioVariables,
    }),
    DateInput: DateInput.extend({
      defaultProps: {
        radius: 'md',
        size: 'xs',
      },
      vars: getInputVariables,
    }),
    Button: Button.extend({
      defaultProps: {
        radius: 'md',
        size: 'md',
        fw: 400,
        c: 'var(--mantine-color-dark-9)',
      },
      vars: getButtonVariables,
    }),
    ActionIcon: ActionIcon.extend({
      vars: getActionIconButtonVariables,
    }),
    Badge: {
      styles: () => ({
        root: {
          textTransform: 'none',
        },
      }),
    },
    Paper: Paper.extend({
      defaultProps: {
        radius: '0.5rem',
      },
    }),
    Menu: Menu.extend({
      styles: {
        dropdown: {
          zIndex: 100,
        },
      },
    }),
  },
});

const mantineTheme = mergeMantineTheme(DEFAULT_THEME, themeOverride);

export const ThemeProvider = ({ children }: { children: ReactNode }) => (
  <MantineProvider theme={mantineTheme} forceColorScheme="light">
    {children}
  </MantineProvider>
);
