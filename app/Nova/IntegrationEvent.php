<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Models\IntegrationEvent as IntegrationEventModel;
use App\Nova\Filters\IntegrationEventStatusFilter;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Badge;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

final class IntegrationEvent extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<IntegrationEventModel>
     */
    public static $model = IntegrationEventModel::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'action';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'status', 'action', 'subject_type', 'subject_id',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable()->hideFromIndex(),
            Text::make('Action'),
            Badge::make('Status')
                ->map(IntegrationEventStatus::colors()),
            Code::make('Metadata')->json(),
            BelongsTo::make('Session', 'session', IntegrationSession::class),
            DateTime::make('Created At'),
        ];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new IntegrationEventStatusFilter,
        ];
    }
}
