<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use App\Http\Resources\Cart\Vendor as CartVendorResource;
use App\Models\BudgetSummary;
use App\Modules\Promotion\Services\ProductPromotionService;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Cart extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $clinic = $request->clinic()->load('connectedVendors');
        $itemsWithPromotions = $this->processItemPromotions($clinic);
        $vendors = $this->groupItemsByVendor($itemsWithPromotions);

        $totalShippingFee = $vendors->sum(function (array $vendor) {
            [, $shippingFee] = $vendor['vendor']->calculateShippingFee($vendor['items']->sum('subtotal'));

            return $shippingFee;
        });

        return [
            'budget' => new Budget(new BudgetSummary($this->clinic, $this->resource)),

            'subtotal' => Money::ofMinor($this->subtotal, 'USD')->getAmount(),

            'total' => Money::ofMinor($this->subtotal + $totalShippingFee, 'USD')->getAmount(),

            'itemsCount' => $this->items->sum('quantity'),

            'uniqueItemsCount' => $this->items->count(),

            'vendors' => CartVendorResource::collection($vendors->sortBy('vendor.name')->values()),
        ];
    }

    /**
     * Process promotions for each cart item
     */
    private function processItemPromotions(\App\Models\Clinic $clinic): Collection
    {
        $promotionService = app(ProductPromotionService::class);

        return $this->items->map(function ($item) use ($clinic, $promotionService) {
            // Get promotions for this specific product offer if it exists
            if ($item->productOffer) {
                $promotions = $promotionService->getActivePromotionsForProductOffer(
                    $item->productOffer,
                    $clinic
                );

                // Append promotions to the item
                $item->promotions = $promotions;
            } else {
                // No product offer, so no promotions
                $item->promotions = collect();
            }

            return $item;
        });
    }

    /**
     * Group items by vendor
     */
    private function groupItemsByVendor(Collection $items): \Illuminate\Support\Collection
    {
        return $items->groupBy('vendor.id')->map(fn (Collection $items) => [
            'vendor' => $items->first()->vendor,
            'items' => $items,
        ]);
    }
}
