<?php

declare(strict_types=1);

namespace App\Services\Sorting;

use App\Models\Clinic;
use App\Models\ProductOffer;
use Illuminate\Support\Collection;

final class ProductOfferSorter
{
    /*
    - Custom product offer sorting
    Rule:
            - Clinics under GPOs
                - Order by GPO recommended vendors (BUGTICKET ENG-600 https://highfivevet.atlassian.net/browse/ENG-643) [PRIORITY 1]
                - Sort by price (asc) [PRIORITY 2]
            -  Independent clinics
                - Sort by price (asc) [PRIORITY 1]
    */

    public static function sort(Collection $offers, string $clinicId): Collection
    {
        // Load the vendor relationship if not already loaded
        if (! $offers->isEmpty()) {
            $offers = $offers->map(function ($offer) {
                return $offer->load(['vendor', 'gpos']);
            });
        }

        $clinic = Clinic::find($clinicId);
        $clinicGpo = $clinic->gpo;

        // For Independent clinics - sort by price (asc)
        if (! $clinicGpo) {
            // temporary test disabling price sorting
            return $offers;

            /*return $offers->sort(function (ProductOffer $a, ProductOffer $b) use ($clinic) {
                $priceA = $a->clinics->where('id', $clinic->id)->first()?->pivot->price ?? $a->price;
                $priceB = $b->clinics->where('id', $clinic->id)->first()?->pivot->price ?? $b->price;

                return $priceA >= $priceB;
            });*/
        }

        // TEMPORARY WORKAROUND - stop sorting by price default
        return $offers->filter(function (ProductOffer $offer) use ($clinicGpo) {
            return $clinicGpo->recommendedVendors->contains($offer->vendor);
        });

        // For GPO clinics - (if clinic belongs to the GPO and product offer vendor is recommended by GPO)
        $recommendedOffers = $offers->filter(function (ProductOffer $offer) use ($clinicGpo) {
            return $clinicGpo->recommendedVendors->contains($offer->vendor);
        })->sort(function (ProductOffer $a, ProductOffer $b) use ($clinicGpo) {
            // Order (Lower order value means higher priority)
            // Assign high order value if not recommended
            $OfferOrderA = $clinicGpo->recommendedVendors->where('id', $a->vendor->id)->first()?->pivot->order;
            $OfferOrderB = $clinicGpo->recommendedVendors->where('id', $b->vendor->id)->first()?->pivot->order;

            if ($OfferOrderA < $OfferOrderB) {
                return -1;
            }
            if ($OfferOrderA > $OfferOrderB) {
                return 1;
            }
        });

        $nonRecommendedOffers = $offers->diff($recommendedOffers)->sort(function (ProductOffer $a, ProductOffer $b) use ($clinic) {
            $priceA = $a->clinics->where('id', $clinic->id)->first()?->pivot->price ?? $a->price;
            $priceB = $b->clinics->where('id', $clinic->id)->first()?->pivot->price ?? $b->price;

            return $priceA >= $priceB;
        });

        return $recommendedOffers->merge($nonRecommendedOffers);
    }
}
