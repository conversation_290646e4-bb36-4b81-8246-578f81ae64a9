<?php

declare(strict_types=1);

namespace App\Actions\Products;

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionRole;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Elastic\Elasticsearch\Client;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log as FacadesLog;
use OpenSearchDSL\Query\Compound\BoolQuery;
use OpenSearchDSL\Query\Compound\FunctionScoreQuery;
use OpenSearchDSL\Query\FullText\MultiMatchQuery;
use OpenSearchDSL\Query\Span\SpanFirstQuery;
use OpenSearchDSL\Query\Span\SpanTermQuery;
use OpenSearchDSL\Query\TermLevel\TermQuery;
use OpenSearchDSL\Query\TermLevel\TermsQuery;
use OpenSearchDSL\Search;
use Throwable;

final class SearchEngine
{
    private const RECENT_ORDERS_DAYS = 30;

    public function __construct(
        private readonly Client $client
    ) {}

    public function handle(Clinic $clinic, string $searchTerm, array $filter = []): ?array
    {
        $vendorIds = $this->getConnectedVendorIds($clinic);

        if (isset($filter['vendor_ids'])) {
            $vendorIds = array_values(array_intersect($vendorIds, explode(',', $filter['vendor_ids'])));
        }

        $mainQuery = $this->buildMainQuery($searchTerm, $vendorIds);
        $functionScoreQuery = $this->buildFunctionScoreQuery($mainQuery, $clinic);

        $search = new Search();
        $search->addQuery($functionScoreQuery);
        $search->setSize(1000);

        $queryArray = $search->toArray();

        // Debug: Log the generated DSL query
        /*FacadesLog::debug('Elasticsearch DSL Query Generated', [
            'query' => json_encode($queryArray, JSON_PRETTY_PRINT),
        ]);*/

        try {
            $response = $this->client->search([
                'index' => (new Product())->searchableAs(),
                'body' => [
                    ...$queryArray,
                    '_source' => false,
                ],
            ]);

            $productIds = collect($response['hits']['hits'] ?? [])
                ->map(fn ($hit) => $hit['_id'])
                ->all();

            $products = $this->fetchProducts($productIds, $clinic, $vendorIds);

            return [$products, $productIds];
        } catch (Throwable $e) {
            FacadesLog::error('Search query failed', [
                'query' => $queryArray,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    private function getConnectedVendorIds(Clinic $clinic): array
    {
        return IntegrationConnection::query()
            ->whereIn('status', [IntegrationConnectionStatus::Connecting, IntegrationConnectionStatus::Connected])
            ->where('clinic_id', $clinic->id)
            ->pluck('vendor_id')
            ->unique()
            ->values()
            ->toArray();
    }

    private function buildMainQuery(string $searchTerm, array $connectedVendorIds): BoolQuery
    {
        $boolQuery = new BoolQuery();

        $vendorSkusQuery = new TermQuery('vendorSkus', $searchTerm, ['boost' => 5.0]);
        $vendorSkusAlphaNumQuery = new TermQuery('vendorSkusAlphaNum', mb_strtolower($searchTerm), ['boost' => 5.0]);
        $manufacturerSkuQuery = new TermQuery('manufacturerSku', $searchTerm, ['boost' => 5.0]);

        $boolQuery->add($vendorSkusQuery, BoolQuery::SHOULD);
        $boolQuery->add($vendorSkusAlphaNumQuery, BoolQuery::SHOULD);
        $boolQuery->add($manufacturerSkuQuery, BoolQuery::SHOULD);

        $multiMatchBestFieldsQuery = new MultiMatchQuery(
            ['name', 'searchTerms'],
            $searchTerm,
            [
                'fuzziness' => 1,
                'type' => 'best_fields',
                'operator' => 'and',
            ],
        );
        $boolQuery->add($multiMatchBestFieldsQuery, BoolQuery::SHOULD);

        $multiMatchPhrasePrefixQuery = new MultiMatchQuery(
            ['name', 'searchTerms'],
            $searchTerm,
            [
                'type' => 'phrase_prefix',
            ],
        );
        $boolQuery->add($multiMatchPhrasePrefixQuery, BoolQuery::SHOULD);

        $spanFirstQuery = new SpanFirstQuery(
            new SpanTermQuery('name', mb_strtolower($searchTerm)),
            1,
            ['boost' => 4]
        );
        $boolQuery->add($spanFirstQuery, BoolQuery::SHOULD);

        $boolQuery->addParameter('minimum_should_match', '1');

        $mainQuery = new BoolQuery;
        $mainQuery->add($boolQuery, BoolQuery::MUST);

        $filterQuery = new TermsQuery('vendorIds', $connectedVendorIds);
        $mainQuery->add($filterQuery, BoolQuery::FILTER);

        return $mainQuery;
    }

    private function buildFunctionScoreQuery(BoolQuery $mainQuery, Clinic $clinic): FunctionScoreQuery
    {
        $functionScoreQuery = new FunctionScoreQuery($mainQuery, [
            'score_mode' => 'max',
            'boost_mode' => 'multiply',
        ]);

        $this->addNamedScoringFunctions($functionScoreQuery, $clinic);

        return $functionScoreQuery;
    }

    private function fetchProducts(array $productIds, Clinic $clinic, array $filterVendorIds): Builder
    {
        // dd($productIds);
        $vendorIds = $clinic->vendors->pluck('id')->unique()->values()->toArray();

        return Product::query()
            ->whereHas('productOffers', function ($query) use ($clinic, $filterVendorIds) {
                $query->whereVendorIdIn($filterVendorIds)
                    ->withVendorConnected([$clinic->id])
                    ->withPriceForFrontend($clinic)
                    ->withActiveOffers()
                    ->where(function ($priceQuery) use ($clinic) {
                        $priceQuery->where(function ($subQuery) use ($clinic) {
                            $subQuery->whereHas('clinics', function ($clinicQuery) use ($clinic) {
                                $clinicQuery->where('clinic_id', $clinic->id)
                                    ->whereNotNull('clinic_product_offer.price');
                            });
                        })->orWhereNotNull('price');
                    });
            })
            ->with(['productOffers' => function ($query) use ($clinic, $vendorIds) {
                $query->whereVendorIdIn($vendorIds)
                    ->withClinic($clinic->id)
                    ->withActiveOffers()
                    ->withLastClinicOrderItem($clinic->id)
                    ->withClinicCartItem($clinic->id)
                    ->withVendorConnected([$clinic->id])
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                    ->withPriceForFrontend($clinic);
            }])
            ->whereIn('id', $productIds);

    }

    private function addNamedScoringFunctions(FunctionScoreQuery $functionScoreQuery, Clinic $clinic): void
    {
        $scoringFunctions = $this->getScoringFunctionDefinitions($clinic);

        foreach ($scoringFunctions as $functionName => $functionData) {
            $this->addNamedFunction(
                $functionScoreQuery,
                $functionName,
                $functionData['weight'],
                $functionData['target_ids'],
                $functionData['field'] ?? 'id'
            );
        }
    }

    private function addNamedFunction(
        FunctionScoreQuery $functionScoreQuery,
        string $functionName,
        float $weight,
        array $targetIds,
        string $field = 'id'
    ): void {
        if (empty($targetIds)) {
            return;
        }

        FacadesLog::debug("Applying named function: {$functionName}", [
            'weight' => $weight,
            'target_count' => count($targetIds),
            'field' => $field,
        ]);

        $functionScoreQuery->addWeightFunction(
            $weight,
            new TermsQuery($field, $targetIds),
        );
    }

    private function getScoringFunctionDefinitions(Clinic $clinic): array
    {
        $clinicPrimaryDistributorId = $clinic->integrationConnections()
            ->where(['role' => IntegrationConnectionRole::PrimaryDistributor])
            ->first()?->vendor_id;

        $clinicSecondaryDistributorId = $clinic->integrationConnections()
            ->where(['role' => IntegrationConnectionRole::SecondaryDistributor])
            ->first()?->vendor_id;

        $preferredManufacturerIds = $clinic->integrationConnections()
            ->where('role', IntegrationConnectionRole::PreferredManufacturer)
            ->pluck('vendor_id')
            ->unique()
            ->values()
            ->toArray();

        $gpoRecommendedVendorIds = $this->getGpoRecommendedVendorIds($clinic);

        $hasAtLeastOneConfigured = $clinicPrimaryDistributorId !== null
            || $clinicSecondaryDistributorId !== null
            || count($preferredManufacturerIds) > 0
            || count($gpoRecommendedVendorIds) > 0;

        $functions = [];

        // Compounding Pharmacy Boost
        if (! $hasAtLeastOneConfigured) {
            $compoundingVendorIds = $this->getCompoundingPharmacyVendorIds();
            if (count($compoundingVendorIds) > 0) {
                $functions['compounding_pharmacy_boost'] = [
                    'enabled' => true,
                    'weight' => 0.75,
                    'target_ids' => $compoundingVendorIds,
                    'field' => 'vendorIds',
                    'description' => 'Boost products from compounding pharmacies',
                ];
            }
        }

        // GPO Recommended Boost
        if ($clinic->account?->gpo?->id !== null) {
            $gpoRecommendedIds = $this->getGpoRecommendedProductIds($clinic);
            if (count($gpoRecommendedIds) > 0) {
                $functions['gpo_recommended_boost'] = [
                    'enabled' => true,
                    'weight' => 20.0,
                    'target_ids' => $gpoRecommendedIds,
                    'field' => 'id',
                    'description' => 'Boost GPO recommended products',
                ];
            }
        }

        // Favorite Products Boost
        $favoriteProductIds = $this->getFavoriteProductIds($clinic);
        if (count($favoriteProductIds) > 0) {
            $functions['favorite_products_boost'] = [
                'enabled' => true,
                'weight' => 5.0,
                'target_ids' => $favoriteProductIds,
                'field' => 'id',
                'description' => 'Boost clinic favorite products',
            ];
        }

        // Recently Ordered Boost
        $recentlyOrderedIds = $this->getRecentlyOrderedProductIds($clinic);
        if (count($recentlyOrderedIds) > 0) {
            $functions['recently_ordered_boost'] = [
                'enabled' => true,
                'weight' => 3.0,
                'target_ids' => $recentlyOrderedIds,
                'field' => 'id',
                'description' => 'Boost recently ordered products',
            ];
        }

        // Primary Distributor Boost
        if ($clinicPrimaryDistributorId !== null) {
            $functions['primary_distributor_boost'] = [
                'enabled' => true,
                'weight' => 2.0,
                'target_ids' => [$clinicPrimaryDistributorId],
                'field' => 'vendorIds',
                'description' => 'Boost products from primary distributor',
            ];
        }

        // Secondary Distributor Boost
        if ($clinicSecondaryDistributorId !== null) {
            $functions['secondary_distributor_boost'] = [
                'enabled' => true,
                'weight' => 2.0,
                'target_ids' => [$clinicSecondaryDistributorId],
                'field' => 'vendorIds',
                'description' => 'Boost products from secondary distributor',
            ];
        }

        // Preferred Manufacturers Boost
        if (count($preferredManufacturerIds) > 0) {
            $functions['preferred_manufacturers_boost'] = [
                'enabled' => true,
                'weight' => 2.0,
                'target_ids' => $preferredManufacturerIds,
                'field' => 'vendorIds',
                'description' => 'Boost products from preferred manufacturers',
            ];
        }

        // GPO Recommended Vendors Boost
        if (count($gpoRecommendedVendorIds) > 0) {
            $functions['gpo_recommended_vendors_boost'] = [
                'enabled' => true,
                'weight' => 20.0,
                'target_ids' => $gpoRecommendedVendorIds,
                'field' => 'vendorIds',
                'description' => 'Boost products from GPO recommended vendors',
            ];
        }

        return $functions;
    }

    private function getCompoundingPharmacyVendorIds(): array
    {
        $wedgewood = Vendor::query()->where('slug', 'wedgewood')->first();

        return $wedgewood ? [$wedgewood->id] : [];
    }

    private function getGpoRecommendedProductIds(Clinic $clinic): array
    {
        $gpoId = $clinic->account?->gpo?->id;

        if (! $gpoId) {
            return [];
        }

        return ProductOffer::whereHas('gpos', function (Builder $query) use ($gpoId) {
            $query->where('gpo_recommended_products.gpo_account_id', $gpoId);
        })->pluck('product_id')->unique()->values()->toArray();
    }

    private function getFavoriteProductIds(Clinic $clinic): array
    {
        return $clinic->favoriteProducts()
            ->pluck('clinic_product_favorites.product_id')
            ->unique()
            ->values()
            ->toArray();
    }

    private function getRecentlyOrderedProductIds(Clinic $clinic): array
    {
        return ProductOffer::whereHas('orderItems', function (Builder $query) use ($clinic) {
            $query->whereHas('order', function (Builder $orders) use ($clinic) {
                $orders->where('clinic_id', $clinic->id)
                    ->where('created_at', '>=', now()->subDays(self::RECENT_ORDERS_DAYS));
            });
        })->pluck('product_id')->unique()->values()->toArray();
    }

    private function getPreferredVendorIds(): array
    {
        return Vendor::query()
            ->whereIn('slug', ['mwi', 'patterson'])
            ->pluck('id')
            ->unique()
            ->values()
            ->toArray();
    }

    private function getGpoRecommendedVendorIds(Clinic $clinic): array
    {
        $gpo = $clinic->account?->gpo;

        if (! $gpo) {
            return [];
        }

        return $gpo->recommendedVendors()
            ->pluck('vendor_id')
            ->unique()
            ->values()
            ->toArray();
    }
}
