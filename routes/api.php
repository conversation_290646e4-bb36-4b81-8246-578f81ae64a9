<?php

declare(strict_types=1);

use App\Http\Controllers\Api\FeatureFlagController;
use App\Http\Controllers\ClinicBudgetSettingsController;
use App\Http\Controllers\ClinicCartSummaryController;
use App\Http\Controllers\Clinics\AutoCompleteController;
use App\Http\Controllers\Clinics\CartController;
use App\Http\Controllers\Clinics\CartItemController;
use App\Http\Controllers\Clinics\FavoriteProductController;
use App\Http\Controllers\Clinics\Orders\ClinicOrderController;
use App\Http\Controllers\Clinics\ProductController;
use App\Http\Controllers\Clinics\ProductOfferController;
use App\Http\Controllers\Clinics\SearchController;
use App\Http\Controllers\Clinics\Settings\ClinicControlledDrugsSettingsController;
use App\Http\Controllers\ClinicVendorController;
use App\Http\Controllers\MoniteEntityUserTokenController;
use App\Http\Controllers\OAuth2\AmazonBusinessController;
use App\Http\Controllers\PasswordResetController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserPasswordController;
use App\Modules\Product\Http\Controllers\PurchaseHistoryController;
use App\Modules\User\Http\Controllers\StopImpersonateUserController;
use Illuminate\Support\Facades\Route;

Route::patch('/users/me/password', [UserPasswordController::class, 'update']);
Route::post('/password-resets', [PasswordResetController::class, 'store']);

Route::middleware('guest')->group(function () {
    /**
     * @deprecated
     */
    Route::post('/users', [UserController::class, 'store']);
});

Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('/users')->group(function () {
        Route::get('/me', [UserController::class, 'show']);
        Route::get('/stop-impersonate', StopImpersonateUserController::class);
    });

    // Monite entity user token generation
    Route::post('/monite/entity-user-token', [MoniteEntityUserTokenController::class, 'store'])
        ->middleware('header:Highfive-Clinic');

    // Feature flags endpoints
    Route::post('/feature-flags/check-monite', [FeatureFlagController::class, 'checkMoniteStatus'])
        ->middleware('header:Highfive-Clinic');

    Route::prefix('/cart')->group(function () {
        Route::get('/', [CartController::class, 'show']);
        Route::get('/promotions', [CartController::class, 'promotions']);
        Route::delete('/', [CartController::class, 'destroy']);
        Route::post('/cart-items', [CartController::class, 'create']);
        Route::patch('/cart-items/{item}', [CartItemController::class, 'update']);
    });

    Route::prefix('/clinics/{clinic}')->group(function () {

        Route::prefix('/budget-settings')->group(function () {
            Route::get('/', [ClinicBudgetSettingsController::class, 'show']);
            Route::put('/', [ClinicBudgetSettingsController::class, 'update']);
        });

        Route::prefix('/settings')->group(function () {
            Route::get('/controlled-drugs', [ClinicControlledDrugsSettingsController::class, 'show']);
            Route::put('/controlled-drugs', [ClinicControlledDrugsSettingsController::class, 'update']);
        });

        Route::prefix('/vendors')->group(function () {
            Route::get('/', [ClinicVendorController::class, 'index']);
            Route::post('/', [ClinicVendorController::class, 'store']);
        });

        /**
         * @deprecated
         */
        Route::get('/cart-summary', [ClinicCartSummaryController::class, 'show']);

        Route::get('/products/{product}', [ProductController::class, 'show']);
        Route::get('/products/{product}/promotions', [ProductController::class, 'promotions']);
        Route::get('/products/{product}/alternatives', [ProductController::class, 'alternatives']);
        Route::get('/product-offers/{productOffer}', [ProductOfferController::class, 'show']);
        Route::get('/product-offers/{productOffer}/purchase-history', [PurchaseHistoryController::class, 'show']);

        Route::prefix('/favorite-products')->group(function () {
            Route::post('/', [FavoriteProductController::class, 'store']);
            Route::delete('/{product}', [FavoriteProductController::class, 'destroy'])->scopeBindings();
        });

        Route::get('/search', [SearchController::class, 'index']);
        Route::get('/autocomplete', [AutoCompleteController::class, 'index']);

        Route::prefix('/orders')->group(function () {
            Route::post('/', [ClinicOrderController::class, 'store']);
        });
    });
});

/**
 * @deprecated
 */
Route::get('/oauth2/amazon-business', [AmazonBusinessController::class, 'index']);
