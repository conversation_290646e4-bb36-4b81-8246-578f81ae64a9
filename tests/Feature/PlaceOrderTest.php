<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Enums\PaymentMethod;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Jobs\Vendor\PlaceOrderToVendor;
use App\Notifications\OrderPlacedForClinic;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use Illuminate\Testing\TestResponse;

function placeOrder(string $clinicId, array $data): TestResponse
{
    return test()->postJson("/api/clinics/{$clinicId}/orders", $data);
}

function getOrderData(array $overrides = []): array
{
    return array_merge([
        'paymentMethod' => PaymentMethod::Invoice->value,
        'billingAddress' => [
            'street' => '123 Main St',
            'city' => 'Anytown',
            'state' => 'CA',
            'postalCode' => '12345',
        ],
        'shippingAddress' => [
            'street' => '123 Main St',
            'city' => 'Anytown',
            'state' => 'CA',
            'postalCode' => '12345',
        ],
    ], $overrides);
}

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->clinic = Clinic::factory()->create();
    $this->user->update([
        'account_id' => $this->clinic->account->id,
    ]);
    $this->clinic->users()->attach($this->user);
});

describe('place order', function () {
    describe('basic functionality', function () {
        it('can place an order with multiple items', function () {
            Carbon::setTestNow(Carbon::parse('2024-11-01'));

            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create(['price' => 1500, 'quantity' => 2]);
            CartItem::factory()->for($cart)->create(['price' => 1200, 'quantity' => 1]);

            $this->actingAs($this->user);

            Queue::fake();

            placeOrder($this->clinic->id, getOrderData())
                ->assertCreated()
                ->assertJson([
                    'orderDate' => '2024-11-01T00:00:00+00:00',
                    'totalItems' => 3,
                    'orderTotal' => 42.00,
                    'numberOfVendors' => 2,
                ]);

            $this->assertDatabaseHas('orders', [
                'clinic_id' => $this->clinic->id,
            ]);

            expect($cart->items)->toHaveCount(0);

            // assert default status is pending after order is placed
            $order = $this->clinic->orders()->first();
            expect($order->status)->toBe(OrderItemStatus::Pending);
        });

        it('uses the same shipping address as the billing address when specified', function () {
            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create();

            $data = getOrderData([
                'shippingAddressSameAsBillingAddress' => true,
                'billingAddress' => [
                    'street' => '456 Elm St',
                    'city' => 'Other City',
                    'state' => 'NY',
                    'postalCode' => '67890',
                ],
            ]);

            $this->actingAs($this->user);

            Queue::fake();

            placeOrder($this->clinic->id, $data)
                ->assertCreated()
                ->assertJsonMissing(['shippingAddressSameAsBillingAddress']);
        });

        it('requires items in cart to place an order', function () {
            $this->actingAs($this->user);

            placeOrder($this->clinic->id, getOrderData())
                ->assertUnprocessable()
                ->assertJsonValidationErrors(['cart'])
                ->assertJsonPath('message', 'The cart is empty.');
        });
    });

    describe('vendor and suborder handling', function () {
        it('creates a suborder for each vendor in the cart', function () {
            $vendor = Vendor::factory()->create();

            IntegrationConnection::create([
                'vendor_id' => $vendor->id,
                'clinic_id' => $this->clinic->id,
                'status' => IntegrationConnectionStatus::Connected,
                'credentials' => ['username' => 'test', 'password' => 'test'],
            ]);

            $product = ProductOffer::factory()->for($vendor)->create();

            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create([
                'product_offer_id' => $product->id,
                'price' => 1500,
                'quantity' => 1,
            ]);

            $this->actingAs($this->user);

            Queue::fake();

            placeOrder($this->clinic->id, getOrderData())->assertCreated();

            $order = $this->clinic->orders()->first();

            $this->assertDatabaseCount('sub_orders', 1);
            $this->assertDatabaseHas('sub_orders', [
                'order_id' => $order->id,
                'vendor_id' => $vendor->id,
            ]);
        });

        it('places an order in each vendor', function () {
            Queue::fake();

            $vendor = Vendor::factory()->create();

            IntegrationConnection::create([
                'vendor_id' => $vendor->id,
                'clinic_id' => $this->clinic->id,
                'status' => IntegrationConnectionStatus::Connected,
                'credentials' => ['username' => 'test', 'password' => 'test'],
            ]);

            $product = ProductOffer::factory()->for($vendor)->create();

            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create([
                'product_offer_id' => $product->id,
                'price' => 1500,
                'quantity' => 1,
            ]);

            $this->actingAs($this->user);

            placeOrder($this->clinic->id, getOrderData())->assertCreated();

            Queue::assertPushed(PlaceOrderToVendor::class, 1);
        });
    });

    describe('validation and error handling', function () {
        it('validates required fields', function () {
            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create();

            $this->actingAs($this->user);

            placeOrder($this->clinic->id, [])
                ->assertUnprocessable()
                ->assertJsonValidationErrors([
                    'paymentMethod',
                    'shippingAddress.street',
                    'shippingAddress.city',
                    'shippingAddress.state',
                    'shippingAddress.postalCode',
                ]);
        });

        it('handles invalid payment method', function () {
            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create();

            $this->actingAs($this->user);

            placeOrder($this->clinic->id, getOrderData(['paymentMethod' => 'invalid']))
                ->assertUnprocessable()
                ->assertJsonValidationErrors(['paymentMethod']);
        });
    });

    describe('price calculation', function () {
        it('calculates correct total price', function () {
            $cart = Cart::factory()->for($this->clinic)->create();
            CartItem::factory()->for($cart)->create(['price' => 1000, 'quantity' => 2]);
            CartItem::factory()->for($cart)->create(['price' => 1500, 'quantity' => 1]);
            CartItem::factory()->for($cart)->create(['price' => 2000, 'quantity' => 3]);

            $this->actingAs($this->user);

            Queue::fake();

            placeOrder($this->clinic->id, getOrderData())
                ->assertCreated()
                ->assertJson(['orderTotal' => 95.00]);

            $this->assertDatabaseHas('orders', [
                'clinic_id' => $this->clinic->id,
            ]);
        });
    });

    test('the order placed notification is sent', function () {
        Carbon::setTestNow(Carbon::parse('2024-11-01'));

        $cart = Cart::factory()->for($this->clinic)->create();
        CartItem::factory()->for($cart)->create(['price' => 1500, 'quantity' => 2]);
        CartItem::factory()->for($cart)->create(['price' => 1200, 'quantity' => 1]);

        $this->actingAs($this->user);

        Queue::fake();
        Notification::fake();

        placeOrder($this->clinic->id, getOrderData())
            ->assertCreated()
            ->assertJson([
                'orderDate' => '2024-11-01T00:00:00+00:00',
                'totalItems' => 3,
                'orderTotal' => 42.00,
                'numberOfVendors' => 2,
            ]);

        Notification::assertSentTo($this->user, OrderPlacedForClinic::class);
    });

    test('cart cache is invalidated after successful order placement', function () {
        $vendor = Vendor::factory()->create(['is_enabled' => true]);
        $productOffer = ProductOffer::factory()->for($vendor)->create();
        $this->clinic->productOffers()->attach($productOffer);

        IntegrationConnection::create([
            'vendor_id' => $vendor->id,
            'clinic_id' => $this->clinic->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $cart = Cart::factory()->for($this->clinic)->create();
        CartItem::factory()->for($cart)->create([
            'product_offer_id' => $productOffer->id,
            'price' => 1500,
            'quantity' => 2,
        ]);

        $this->actingAs($this->user);

        $getCartAction = app(App\Modules\Cart\Actions\GetCartAction::class);
        $cachedCart = $getCartAction->handle($this->clinic);
        expect($cachedCart->items)->toHaveCount(1);

        $cacheKey = "clinic_cart_{$this->clinic->id}";
        expect(Cache::has($cacheKey))->toBeTrue();

        Queue::fake();

        placeOrder($this->clinic->id, getOrderData())
            ->assertCreated();

        expect(Cache::has($cacheKey))->toBeFalse('Main cart cache should be invalidated after order placement');

        expect($cart->fresh()->items)->toHaveCount(0);

        $freshCart = $getCartAction->handle($this->clinic);
        expect($freshCart->items)->toHaveCount(0);
    });
});
