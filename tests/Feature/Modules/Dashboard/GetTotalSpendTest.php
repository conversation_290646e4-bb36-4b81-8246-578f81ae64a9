<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $items = OrderItem::factory()->count(1)
        ->recycle(Vendor::factory()->create())
        ->state([
            'status' => OrderItemStatus::Delivered,
            'quantity' => 2,
            'price' => 15000,
        ]);

    // Create orders with different dates
    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has($items, 'items')
        ->createMany([
            ['created_at' => Carbon::parse('2025-01-01')],
            ['created_at' => Carbon::parse('2025-01-15')],
            ['created_at' => Carbon::parse('2025-02-01')],
        ]);

    // Create orders for a different clinic (should not be counted)
    $otherClinic = Clinic::factory()->for($account, 'account')->create();
    Order::factory()
        ->for($otherClinic, 'clinic')
        ->has($items, 'items')
        ->create(['created_at' => Carbon::parse('2025-01-10')]);
});

test('api contract', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/dashboard/metrics/total-spend?filter[date_from]=2025-01-01&filter[date_to]=2025-01-31');

    $response->assertOk()
        ->assertJsonStructure([
            'title',
            'value',
            'description',
            'helpText',
        ]);
});

test('requires highfive-clinic header', function () {
    $this->actingAs($this->user)
        ->getJson('/api/dashboard/metrics/total-spend?filter[date_from]=2025-01-01&filter[date_to]=2025-01-31')
        ->assertBadRequest()
        ->assertJson([
            'message' => 'The highfive-clinic header is required.',
        ]);
});

test('filters by date range correctly', function () {
    // January orders only (2 orders)
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/dashboard/metrics/total-spend?filter[date_from]=2025-01-01&filter[date_to]=2025-01-31');

    $response->assertOk()
        ->assertJsonPath('value', '$600.00');

    // All orders (3 orders)
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/dashboard/metrics/total-spend?filter[date_from]=2025-01-01&filter[date_to]=2025-02-28');

    $response->assertOk()
        ->assertJsonPath('value', '$900.00');
});

test('filters by clinic correctly', function () {
    // Create a different user with access to both clinics
    $accountOwner = User::factory()->for($this->user->account, 'account')->create();
    $accountOwner->assignRole(ClinicAccountRole::Owner);

    // Get metrics for the first clinic
    $response = $this->actingAs($accountOwner)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/dashboard/metrics/total-spend?filter[date_from]=2025-01-01&filter[date_to]=2025-02-28'); // 3 orders

    $response->assertOk()
        ->assertJsonPath('value', '$900.00');

    // Get metrics for the second clinic
    $otherClinic = Clinic::where('id', '!=', $this->clinic->id)->first();

    $response = $this->actingAs($accountOwner)
        ->withHeader('Highfive-Clinic', $otherClinic->id)
        ->getJson('/api/dashboard/metrics/total-spend?filter[date_from]=2025-01-01&filter[date_to]=2025-02-28'); // 1 order

    $response->assertOk()
        ->assertJsonPath('value', '$300.00');
});
