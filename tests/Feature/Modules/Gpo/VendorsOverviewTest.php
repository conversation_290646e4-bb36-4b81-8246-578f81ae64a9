<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Actions\GetVendorsOverview;
use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoAccountSettings;
use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create(['name' => 'Test GPO Account']);

    $this->gpoUser = GpoUser::factory()->create([
        'name' => 'Test GPO User',
        'email' => '<EMAIL>',
        'account_id' => $this->gpoAccount->id,
    ]);

    $this->clinicAccount1 = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);

    $this->clinic1 = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount1->id,
    ]);

    $this->vendor1 = Vendor::factory()->create([
        'name' => 'Test Vendor 1',
        'is_enabled' => true,
    ]);

    $this->vendor2 = Vendor::factory()->create([
        'name' => 'Test Vendor 2',
        'is_enabled' => true,
    ]);

    $this->productOffer1 = ProductOffer::factory()->create([
        'vendor_id' => $this->vendor1->id,
    ]);

    $this->productOffer2 = ProductOffer::factory()->create([
        'vendor_id' => $this->vendor2->id,
    ]);

    $this->order1 = Order::factory()->create([
        'clinic_id' => $this->clinic1->id,
        'created_at' => '2025-01-15',
    ]);

    $this->orderItem1 = OrderItem::factory()->create([
        'order_id' => $this->order1->id,
        'product_offer_id' => $this->productOffer1->id,
        'price' => 5000,
        'quantity' => 1,
        'status' => OrderItemStatus::Delivered,
        'created_at' => '2025-01-15',
    ]);

    $this->orderItem2 = OrderItem::factory()->create([
        'order_id' => $this->order1->id,
        'product_offer_id' => $this->productOffer2->id,
        'price' => 3000,
        'quantity' => 1,
        'status' => OrderItemStatus::Delivered,
        'created_at' => '2025-01-16',
    ]);
});

describe('GetVendorsOverview with Settings', function () {
    it('uses default values when no settings are configured', function () {
        $action = new GetVendorsOverview();
        $result = $action->handle(
            $this->gpoAccount->id,
            Carbon::parse('2025-01-01'),
            Carbon::parse('2025-01-31')
        );

        expect($result['vendors'])->toHaveCount(2);

        // Check that default values are used
        $vendor1Data = $result['vendors']->firstWhere('id', $this->vendor1->id);
        $vendor2Data = $result['vendors']->firstWhere('id', $this->vendor2->id);

        expect($vendor1Data)->not->toBeNull();
        expect($vendor2Data)->not->toBeNull();

        // 5000 / 1000000 = 0.5%
        // 3000 / 1000000 = 0.3%
        expect($vendor1Data->growthTargetPercentage)->toBe(0.5);
        expect($vendor2Data->growthTargetPercentage)->toBe(0.3);
    });

    it('uses custom performance threshold when configured', function () {
        // Create custom performance threshold setting
        GpoAccountSettings::create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 90.0, // Custom threshold
                'notification_message' => 'Custom performance alert',
            ],
        ]);

        $action = new GetVendorsOverview();
        $result = $action->handle(
            $this->gpoAccount->id,
            Carbon::parse('2025-01-01'),
            Carbon::parse('2025-01-31')
        );

        expect($result['vendors'])->toHaveCount(2);

        // Both vendors should have notifications since they're below 90% threshold
        $vendor1Data = $result['vendors']->firstWhere('id', $this->vendor1->id);
        $vendor2Data = $result['vendors']->firstWhere('id', $this->vendor2->id);

        expect($vendor1Data->notification)->toBe('Custom performance alert');
        expect($vendor2Data->notification)->toBe('Custom performance alert');
    });

    it('uses custom vendor goals when configured', function () {
        // Create custom vendor goals setting with array structure
        GpoAccountSettings::create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'vendor_id' => $this->vendor1->id,
                        'goal_amount' => 500000, // $5,000 in cents (custom goal)
                    ],
                    [
                        'enabled' => true,
                        'vendor_id' => $this->vendor2->id,
                        'goal_amount' => 200000, // $2,000 in cents (custom goal)
                    ],
                ],
            ],
        ]);

        $action = new GetVendorsOverview();
        $result = $action->handle(
            $this->gpoAccount->id,
            Carbon::parse('2025-01-01'),
            Carbon::parse('2025-01-31')
        );

        expect($result['vendors'])->toHaveCount(2);

        // Vendor 1 has $50.00 = 5000 cents, goal is 500000 cents
        // Percentage = (5000/500000) * 100 = 1.0%
        $vendor1Data = $result['vendors']->firstWhere('id', $this->vendor1->id);
        expect($vendor1Data->growthTargetPercentage)->toBe(1.0);

        // Vendor 2 has $30.00 = 3000 cents, goal is 200000 cents
        // Percentage = (3000/200000) * 100 = 1.5%
        $vendor2Data = $result['vendors']->firstWhere('id', $this->vendor2->id);
        expect($vendor2Data->growthTargetPercentage)->toBe(1.5);
    });

    it('uses default goal for vendors without specific goals', function () {
        // Create vendor goals setting with only one vendor
        GpoAccountSettings::create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'vendor_id' => $this->vendor1->id,
                        'goal_amount' => 500000, // $5,000 in cents (custom goal)
                    ],
                    // Vendor 2 has no specific goal, should use default
                ],
            ],
        ]);

        $action = new GetVendorsOverview();
        $result = $action->handle(
            $this->gpoAccount->id,
            Carbon::parse('2025-01-01'),
            Carbon::parse('2025-01-31')
        );

        expect($result['vendors'])->toHaveCount(2);

        // Vendor 1 uses custom goal
        $vendor1Data = $result['vendors']->firstWhere('id', $this->vendor1->id);
        expect($vendor1Data->growthTargetPercentage)->toBe(1.0);

        // Vendor 2 uses default goal (1,000,000 cents)
        $vendor2Data = $result['vendors']->firstWhere('id', $this->vendor2->id);
        expect($vendor2Data->growthTargetPercentage)->toBe(0.3);
    });

    it('ignores disabled vendor goals', function () {
        // Create vendor goals setting with one disabled goal
        GpoAccountSettings::create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => false, // Disabled goal
                        'vendor_id' => $this->vendor1->id,
                        'goal_amount' => 500000,
                    ],
                    [
                        'enabled' => true,
                        'vendor_id' => $this->vendor2->id,
                        'goal_amount' => 200000,
                    ],
                ],
            ],
        ]);

        $action = new GetVendorsOverview();
        $result = $action->handle(
            $this->gpoAccount->id,
            Carbon::parse('2025-01-01'),
            Carbon::parse('2025-01-31')
        );

        expect($result['vendors'])->toHaveCount(2);

        // Vendor 1 uses default goal since its goal is disabled
        $vendor1Data = $result['vendors']->firstWhere('id', $this->vendor1->id);
        expect($vendor1Data->growthTargetPercentage)->toBe(0.5);

        // Vendor 2 uses custom goal
        $vendor2Data = $result['vendors']->firstWhere('id', $this->vendor2->id);
        expect($vendor2Data->growthTargetPercentage)->toBe(1.5);
    });
});

describe('GPO Vendors Overview CSV Export', function () {
    it('requires authentication to export data', function () {
        $response = $this->getJson('/api/gpo/vendors-overview/export');

        $response->assertStatus(401);
    });

    it('exports CSV with correct headers for authenticated user', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/vendors-overview/export?'.http_build_query([
            'vendor_id' => $this->vendor1->id,
            'date_from' => '2025-01-01',
            'date_to' => '2025-01-31',
        ]));

        $response->assertOk()
            ->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    });
});
