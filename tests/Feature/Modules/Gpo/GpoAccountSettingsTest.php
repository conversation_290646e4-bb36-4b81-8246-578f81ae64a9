<?php

declare(strict_types=1);

use App\Models\Vendor;
use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoAccountSettings;

describe('GpoAccountSettings Model', function () {
    beforeEach(function () {
        $this->gpoAccount = GpoAccount::factory()->create();
        $this->vendor = Vendor::factory()->create();
    });

    it('can create a new setting', function () {
        $setting = GpoAccountSettings::factory()->create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 85,
                'notification_message' => 'Performance alert enabled',
            ],
        ]);

        expect($setting->gpo_account_id)->toBe($this->gpoAccount->id);
        expect($setting->setting_key)->toBe(GpoSettingKey::PerformanceThreshold->value);
        expect($setting->value)->toBe([
            'enabled' => true,
            'threshold' => 85,
            'notification_message' => 'Performance alert enabled',
        ]);
        expect($setting->created_at)->not->toBeNull();
        expect($setting->updated_at)->not->toBeNull();
    });

    it('belongs to a GPO account', function () {
        $setting = GpoAccountSettings::factory()->create([
            'gpo_account_id' => $this->gpoAccount->id,
        ]);

        expect($setting->gpo)->toBeInstanceOf(GpoAccount::class);
        expect($setting->gpo->id)->toBe($this->gpoAccount->id);
    });

    it('can have multiple settings for the same account', function () {
        $setting1 = GpoAccountSettings::factory()->create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 85,
                'notification_message' => 'Performance alert enabled',
            ],
        ]);

        $setting2 = GpoAccountSettings::factory()->create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'goal_amount' => 1000000,
                        'vendor_id' => $this->vendor->id,
                    ],
                ],
            ],
        ]);

        expect($this->gpoAccount->settings)->toHaveCount(2);
        expect($this->gpoAccount->settings->pluck('setting_key')->toArray())
            ->toContain(GpoSettingKey::PerformanceThreshold->value, GpoSettingKey::VendorGoals->value);
    });

    it('casts value to array', function () {
        $setting = GpoAccountSettings::factory()->create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 85,
                'notification_message' => 'Performance alert enabled',
            ],
        ]);

        expect($setting->value)->toBeArray();
        expect($setting->value['enabled'])->toBeTrue();
        expect($setting->value['threshold'])->toBe(85);
        expect($setting->value['notification_message'])->toBe('Performance alert enabled');
    });

    it('validates performance threshold value structure', function () {
        // Valid value
        $setting = GpoAccountSettings::create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 85,
                'notification_message' => 'Performance alert enabled',
            ],
        ]);

        expect($setting->value['enabled'])->toBeTrue();
        expect($setting->value['threshold'])->toBe(85);
        expect($setting->value['notification_message'])->toBe('Performance alert enabled');
    });

    it('throws exception for invalid performance threshold value', function () {
        expect(function () {
            GpoAccountSettings::create([
                'gpo_account_id' => $this->gpoAccount->id,
                'setting_key' => GpoSettingKey::PerformanceThreshold->value,
                'value' => [
                    'enabled' => 'not_a_boolean', // Invalid type
                    'threshold' => 150, // Invalid range
                    'notification_message' => 'Valid message',
                ],
            ]);
        })->toThrow(InvalidArgumentException::class, 'Validation failed: The enabled field must be true or false.');
    });

    it('validates vendor goals value structure', function () {
        $setting = GpoAccountSettings::create([
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'goal_amount' => 1000000,
                        'vendor_id' => $this->vendor->id,
                    ],
                ],
            ],
        ]);

        expect($setting->value['goals'][0]['goal_amount'])->toBe(1000000);
        expect($setting->value['goals'][0]['vendor_id'])->toBeString();
    });

    it('throws exception for invalid vendor goals value', function () {
        expect(function () {
            GpoAccountSettings::create([
                'gpo_account_id' => $this->gpoAccount->id,
                'setting_key' => GpoSettingKey::VendorGoals->value,
                'value' => [
                    'goals' => [
                        [
                            'enabled' => true,
                            'goal_amount' => 1000000,
                            'vendor_id' => fake()->uuid(),
                        ],
                    ],
                ],
            ]);
        })->toThrow(InvalidArgumentException::class, 'Validation failed: The selected goals.0.vendor_id is invalid.');
    });
});
