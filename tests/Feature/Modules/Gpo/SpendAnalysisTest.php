<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create(['name' => 'Test GPO Account']);

    $this->gpoUser = GpoUser::factory()->create([
        'name' => 'Test GPO User',
        'email' => '<EMAIL>',
        'password' => Hash::make('password123'),
        'account_id' => $this->gpoAccount->id,
    ]);

    // Create test clinic associated with the GPO account
    $this->clinic = Clinic::factory()->create([
        'name' => 'Test Clinic',
        'fulltime_dvm_count' => 3,
        'exam_rooms_count' => 5,
    ]);

    // Associate clinic with GPO account (assuming there's a relationship)
    $this->clinic->account()->update(['gpo_account_id' => $this->gpoAccount->id]);
});

describe('GPO Spend Analysis API', function () {
    it('requires authentication to access spend analysis endpoint', function () {
        $response = $this->getJson('/api/gpo/spend-analysis');

        $response->assertStatus(401);
    });

    it('returns spend analysis data for authenticated GPO user', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis');

        $response->assertOk()
            ->assertJsonStructure([
                'data',
                'pagination' => [
                    'current_page',
                    'per_page',
                    'total',
                    'last_page',
                ],
            ]);
    });

    it('applies filters correctly', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'fulltime_dvm_min' => 2,
            'fulltime_dvm_max' => 5,
            'per_page' => 10,
        ]));

        $response->assertOk();
    });

    it('applies order by correctly', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'order_by' => 'total_spend',
            'direction' => 'desc',
        ]));

        $response->assertOk();
    });

    it('handles date filters', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31',
        ]));

        $response->assertOk();
    });

    it('can filter by practice types', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'practice_types' => ['general', 'emergency_and_urgent_care'],
        ]));

        $response->assertOk();
    });

    it('can filter by spend ranges', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'min_spend' => 1000,
            'max_spend' => 50000,
        ]));

        $response->assertOk();
    });

    it('can filter inactive clinics only', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'inactive_only' => true,
        ]));

        $response->assertOk();
    });

    it('validates filter parameters correctly', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'fulltime_dvm_min' => -1, // Invalid: negative number
            'max_spend' => 100,
            'min_spend' => 200, // Invalid: max < min
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fulltime_dvm_min', 'max_spend']);
    });

    it('can filter by specific clinic IDs', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'clinic_ids' => [$this->clinic->id],
        ]));

        $response->assertOk();
    });

    it('validates clinic IDs exist and belong to GPO', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        // Create a clinic not belonging to this GPO
        $otherGpoAccount = GpoAccount::factory()->create(['name' => 'Other GPO']);
        $otherClinic = Clinic::factory()->create(['name' => 'Other Clinic']);
        $otherClinic->account()->update(['gpo_account_id' => $otherGpoAccount->id]);

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'clinic_ids' => [$otherClinic->id],
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['clinic_ids.0']);
    });

    it('validates clinic IDs are valid UUIDs', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis?'.http_build_query([
            'clinic_ids' => ['invalid-uuid'],
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['clinic_ids.0']);
    });
});

describe('GPO Spend Analysis CSV Export', function () {
    it('requires authentication to export data', function () {
        $response = $this->getJson('/api/gpo/spend-analysis/export');

        $response->assertStatus(401);
    });

    it('exports CSV with correct headers for authenticated user', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis/export');

        $response->assertOk()
            ->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    });

    it('applies filters to exported data', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis/export?'.http_build_query([
            'fulltime_dvm_min' => 2,
            'order_by' => 'total_spend',
        ]));

        $response->assertOk();
    });

    it('applies clinic ID filters to exported data', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis/export?'.http_build_query([
            'clinic_ids' => [$this->clinic->id],
        ]));

        $response->assertOk()
            ->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    });
});

describe('GPO Spend Analysis Summary', function () {
    it('returns spend analysis summary data for authenticated GPO user', function () {
        Sanctum::actingAs($this->gpoUser, [], 'gpo');

        $response = $this->getJson('/api/gpo/spend-analysis/summary');

        $response->assertOk();
    });
});
