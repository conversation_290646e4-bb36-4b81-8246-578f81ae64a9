<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Cart\Actions\AddCartItemsAction;
use App\Modules\Cart\Actions\CleanCartAction;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    $this->getCartAction = app(GetCartAction::class);
    $this->addCartItemsAction = app(AddCartItemsAction::class);
    $this->cleanCartAction = app(CleanCartAction::class);
});

describe('Cart Actions', function () {
    it('GetCartAction caches cart data', function () {
        // First call should cache the cart
        $cart1 = $this->getCartAction->handle($this->clinic);

        // Second call should use cache
        $cart2 = $this->getCartAction->handle($this->clinic);

        expect($cart1->id)->toBe($cart2->id);

        // Verify cache exists
        $cacheKey = "clinic_cart_{$this->clinic->id}";
        expect(Cache::has($cacheKey))->toBeTrue();
    });

    it('GetCartAction forgets cache when requested', function () {
        // Create cache
        $this->getCartAction->handle($this->clinic);

        // Forget cache
        $this->getCartAction->forgetCache($this->clinic);

        // Verify cache is cleared
        $cacheKey = "clinic_cart_{$this->clinic->id}";
        expect(Cache::has($cacheKey))->toBeFalse();
    });

    it('AddCartItemsAction adds items', function () {
        $items = [
            [
                'product_offer_id' => $this->productOffer->id,
                'quantity' => 2,
                'notes' => 'Test notes',
            ],
        ];

        $this->addCartItemsAction->handle($this->clinic, $items);

        // Verify items were added
        $cart = $this->clinic->cart()->first();
        expect($cart->items)->toHaveCount(1);
        expect($cart->items->first()->quantity)->toBe(2);
    });

    it('AddCartItemsAction handles invalid product offers gracefully', function () {
        $items = [
            [
                'product_offer_id' => '00000000-0000-0000-0000-000000000000',
                'quantity' => 1,
            ],
        ];

        // Should not throw exception
        $this->addCartItemsAction->handle($this->clinic, $items);

        // Verify no items were added
        $cart = $this->clinic->cart()->first();
        expect($cart->items)->toHaveCount(0);
    });

    it('AddCartItemsAction removes items when quantity is zero', function () {
        // First add an item
        $this->addCartItemsAction->handle($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 1],
        ]);

        // Then remove it
        $this->addCartItemsAction->handle($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 0],
        ]);

        // Verify item was removed
        $cart = $this->clinic->cart()->first();
        expect($cart->items)->toHaveCount(0);
    });

    it('CleanCartAction cleans cart', function () {
        // First add items
        $this->addCartItemsAction->handle($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 1],
        ]);

        // Then clean cart
        $this->cleanCartAction->handle($this->clinic);

        // Verify cart is cleaned
        $cart = $this->clinic->cart()->first();
        expect($cart->items)->toHaveCount(0);
    });

    it('actions clear cache after modifications', function () {
        // Create initial cache
        $this->getCartAction->handle($this->clinic);

        // Add items (should clear cache)
        $this->addCartItemsAction->handle($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 1],
        ]);

        // Verify cache was recreated (not necessarily immediately, but eventually)
        $cart = $this->getCartAction->handle($this->clinic);
        expect($cart)->not()->toBeNull();
        expect($cart->items)->toHaveCount(1);
    });
});
