<?php

declare(strict_types=1);

use App\Modules\Cart\Data\CartItemData;

describe('CartItemData', function () {
    it('creates cart item data from array', function () {
        $data = [
            'product_offer_id' => 'test-uuid',
            'quantity' => 5,
            'notes' => 'Test notes',
        ];

        $cartItem = CartItemData::fromArray($data);

        expect($cartItem->product_offer_id)->toBe('test-uuid');
        expect($cartItem->quantity)->toBe(5);
        expect($cartItem->notes)->toBe('Test notes');
    });

    it('creates cart item data with optional notes', function () {
        $data = [
            'product_offer_id' => 'test-uuid',
            'quantity' => 3,
        ];

        $cartItem = CartItemData::fromArray($data);

        expect($cartItem->product_offer_id)->toBe('test-uuid');
        expect($cartItem->quantity)->toBe(3);
        expect($cartItem->notes)->toBeNull();
    });

    it('converts to array correctly', function () {
        $cartItem = new CartItemData('test-uuid', 2, 'Test notes');

        $array = $cartItem->toArray();

        expect($array)->toBe([
            'product_offer_id' => 'test-uuid',
            'quantity' => 2,
            'notes' => 'Test notes',
        ]);
    });

    it('converts to array with null notes', function () {
        $cartItem = new CartItemData('test-uuid', 1);

        $array = $cartItem->toArray();

        expect($array)->toBe([
            'product_offer_id' => 'test-uuid',
            'quantity' => 1,
            'notes' => null,
        ]);
    });

    it('handles zero quantity', function () {
        $cartItem = new CartItemData('test-uuid', 0, 'Remove item');

        expect($cartItem->quantity)->toBe(0);
        expect($cartItem->notes)->toBe('Remove item');
    });

    it('handles large quantities', function () {
        $cartItem = new CartItemData('test-uuid', 999999);

        expect($cartItem->quantity)->toBe(999999);
    });
});
