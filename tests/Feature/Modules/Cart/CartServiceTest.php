<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Cart\Services\CartService;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    $this->cartService = app(CartService::class);
});

describe('CartService', function () {
    it('gets cart with optimized loading', function () {
        $cart = $this->cartService->getCart($this->clinic);

        expect($cart)->not()->toBeNull();
        expect($cart->clinic_id)->toBe($this->clinic->id);
    });

    it('adds items to cart with batch processing', function () {
        $items = [
            [
                'product_offer_id' => $this->productOffer->id,
                'quantity' => 2,
                'notes' => 'Test notes',
            ],
        ];

        $cart = $this->cartService->addItems($this->clinic, $items);

        expect($cart->items)->toHaveCount(1);
        expect($cart->items->first()->quantity)->toBe(2);
        expect($cart->items->first()->notes)->toBe('Test notes');
    });

    it('updates item in cart', function () {
        // First add an item
        $this->cartService->addItems($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 1],
        ]);

        // Then update it
        $cart = $this->cartService->updateItem($this->clinic, $this->productOffer->id, 5, 'Updated notes');

        expect($cart->items->first()->quantity)->toBe(5);
        expect($cart->items->first()->notes)->toBe('Updated notes');
    });

    it('removes item from cart', function () {
        // First add an item
        $this->cartService->addItems($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 1],
        ]);

        // Then remove it
        $cart = $this->cartService->removeItem($this->clinic, $this->productOffer->id);

        expect($cart->items)->toHaveCount(0);
    });

    it('cleans cart completely', function () {
        // First add items
        $this->cartService->addItems($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 1],
        ]);

        // Then clean it
        $cart = $this->cartService->cleanCart($this->clinic);

        expect($cart->items)->toHaveCount(0);
    });

    it('gets cart summary', function () {
        // Add items to cart
        $this->cartService->addItems($this->clinic, [
            ['product_offer_id' => $this->productOffer->id, 'quantity' => 2],
        ]);

        $summary = $this->cartService->getCartSummary($this->clinic);

        expect($summary)->toHaveKeys([
            'total_items',
            'total_price',
            'unique_items',
            'prices_have_changed',
            'items_count',
        ]);

        expect($summary['total_items'])->toBe(2);
        expect($summary['unique_items'])->toBe(1);
        expect($summary['items_count'])->toBe(1);
    });

    it('gets promotions for cart', function () {
        $promotions = $this->cartService->getPromotions($this->clinic);

        expect($promotions)->toBeArray();
    });

    it('clears all caches', function () {
        // Create cache first
        $this->cartService->getCart($this->clinic);

        // Clear all caches
        $this->cartService->clearAllCaches($this->clinic);

        // Verify caches are cleared by checking if fresh data is loaded
        $cart = $this->cartService->getCart($this->clinic);
        expect($cart)->not()->toBeNull();
    });

    it('handles products with invalid prices gracefully', function () {
        // Create a product offer with no price
        $invalidProductOffer = ProductOffer::factory()->for($this->vendor)->create([
            'price' => null,
        ]);

        $items = [
            [
                'product_offer_id' => $invalidProductOffer->id,
                'quantity' => 1,
            ],
        ];

        // Should throw an exception for invalid price
        expect(fn () => $this->cartService->addItems($this->clinic, $items))
            ->toThrow(InvalidArgumentException::class, "Product offer {$invalidProductOffer->id} has no valid price");
    });
});
