<?php

declare(strict_types=1);

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\SavedItems\Actions\AddToCartFromSavedItemsAction;
use App\Modules\SavedItems\Actions\AddToSavedItemsAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Facades\Cache;

describe('Actions', function () {
    it('creates a saved item and removes the cart item', function () {
        $cartItem = CartItem::factory()->create();
        $action = app(AddToSavedItemsAction::class);
        $action->handle([$cartItem->id]);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => $cartItem->quantity,
        ]);

        // Verify the cart item was actually soft deleted
        $this->assertSoftDeleted('cart_items', [
            'id' => $cartItem->id,
        ]);
    });

    it('adds to the saved item quantity', function () {
        $cartItem = CartItem::factory()->create([
            'quantity' => 2,
        ]);
        SavedItem::factory()->create([
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 3,
        ]);
        $action = app(AddToSavedItemsAction::class);
        $action->handle([$cartItem->id]);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 5,
        ]);
    });

    it('removes a saved item and adds it back to the cart', function () {
        $savedItem = SavedItem::factory()->create();
        $action = app(AddToCartFromSavedItemsAction::class);
        $action->handle([$savedItem->id]);

        $this->assertDatabaseHas('cart_items', [
            'product_offer_id' => $savedItem->product_offer_id,
            'quantity' => $savedItem->quantity,
        ]);

        $this->assertDatabaseMissing('saved_items', [
            'id' => $savedItem->id,
        ]);
    });

    it('adds a saved item to the cart and updates the saved item quantity', function () {
        $cartItem = CartItem::factory()->create([
            'quantity' => 10,
        ]);
        $savedItem = SavedItem::factory()->create([
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 1,
        ]);
        $action = app(AddToCartFromSavedItemsAction::class);
        $action->handle([$savedItem->id]);

        $this->assertDatabaseHas('cart_items', [
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 11,
        ]);

        $this->assertDatabaseMissing('saved_items', [
            'id' => $savedItem->id,
        ]);
    });

    it('clears cart cache when moving items to saved items', function () {
        $cartItem = CartItem::factory()->create();
        $clinic = $cartItem->cart->clinic;

        // Create cache first
        $getCartAction = app(App\Modules\Cart\Actions\GetCartAction::class);
        $getCartAction->handle($clinic);

        // Verify cache exists
        $cacheKey = "clinic_cart_{$clinic->id}";
        $this->assertTrue(Cache::has($cacheKey));

        // Move item to saved items
        $action = app(AddToSavedItemsAction::class);
        $action->handle([$cartItem->id]);

        // Verify cache is cleared
        $this->assertFalse(Cache::has($cacheKey));
    });
});

// Api endpoints
describe('Api endpoints', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->account = ClinicAccount::factory()->create();
        $this->user->update([
            'account_id' => $this->account->id,
        ]);
        $this->clinic = Clinic::factory()->create([
            'clinic_account_id' => $this->account->id,
        ]);
        $this->cart = Cart::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);
        $this->cartItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
        ]);
    });

    it('user can see saved items', function () {
        SavedItem::factory()->count(3)->create([
            'clinic_id' => $this->clinic->id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->getJson('/api/saved-items');

        $response->assertStatus(200);

        $response->assertJsonStructure([
            'savedItems' => [
                '*' => [
                    'id',
                    'clinicId',
                    'productOfferId',
                    'quantity',
                    'createdAt',
                    'productOffer' => [
                        'id',
                        'vendor' => [
                            'id',
                            'name',
                            'imageUrl',
                        ],
                        'vendorSku',
                        'price',
                        'clinicPrice',
                        'stockStatus',
                        'increments',
                        'isRecommended',
                    ],
                ],
            ],
        ]);
    });

    it('user can add saved item to their clinic', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_ids' => [$this->cartItem->id],
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'savedItems' => [
                '*' => [
                    'id',
                    'clinicId',
                    'productOfferId',
                    'quantity',
                    'createdAt',
                    'productOffer' => [
                        'id',
                        'vendor' => [
                            'id',
                            'name',
                            'imageUrl',
                        ],
                        'product' => [
                            'id',
                            'name',
                            'imageUrl',
                            'offers',
                        ],
                        'vendorSku',
                        'price',
                        'clinicPrice',
                        'stockStatus',
                        'increments',
                        'isRecommended',
                    ],
                ],
            ],
        ]);
    });

    it('user remove cart item and add it to saved items', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_ids' => [$this->cartItem->id],
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'savedItems' => [
                '*' => [
                    'id',
                    'clinicId',
                    'productOfferId',
                    'quantity',
                    'createdAt',
                    'productOffer' => [
                        'id',
                        'vendor' => [
                            'id',
                            'name',
                            'imageUrl',
                        ],
                        'product' => [
                            'id',
                            'name',
                            'imageUrl',
                            'offers',
                        ],
                        'vendorSku',
                        'price',
                        'clinicPrice',
                        'stockStatus',
                        'increments',
                        'isRecommended',
                    ],
                ],
            ],
        ]);

        // Verify the cart item was actually soft deleted
        $this->assertSoftDeleted('cart_items', [
            'id' => $this->cartItem->id,
        ]);
    });

    it('user can remove saved item', function () {
        $savedItem = SavedItem::factory()->create([
            'clinic_id' => $this->clinic->id,
            'product_offer_id' => $this->cartItem->product_offer_id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->deleteJson('/api/saved-items/remove', [
                'saved_item_ids' => [$savedItem->id],
            ]);

        $response->assertStatus(204);
    });
});
