<?php

declare(strict_types=1);

use App\Enums\ClinicBudgetType;
use App\Enums\ExpenseCategory;
use App\Enums\OrderItemStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Brick\Money\Money;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create([
        'is_enabled' => true,
    ]);
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
    $this->clinic->productOffers()->attach($this->productOffer);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('returns cart data with correct structure', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJsonStructure([
            'budget' => [
                'type',
                'weekToDate' => [
                    '*' => [
                        'category',
                        'spent',
                        'spentPercentage',
                        'target',
                        'targetPercentage',
                    ],
                ],
                'monthToDate' => [
                    '*' => [
                        'category',
                        'spent',
                        'spentPercentage',
                        'target',
                        'targetPercentage',
                    ],
                ],
            ],
            'subtotal',
            'total',
            'itemsCount',
            'uniqueItemsCount',
            'vendors' => [
                '*' => [
                    'id',
                    'name',
                    'imageUrl',
                    'subtotal',
                    'total',
                    'shippingFee',
                    'amountToFreeShipping',
                    'cutoffTime',
                    'items' => [
                        '*' => [
                            'id',
                            'quantity',
                            'price',
                            'subtotal',
                            'notes',
                            'product' => [
                                'id',
                                'name',
                                'imageUrl',
                                'offers' => [
                                    '*' => [
                                        'id',
                                        'price',
                                        'clinicPrice',
                                        'lastOrderedAt',
                                        'lastOrderedQuantity',
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
});

it('returns cart summary data with correct structure', function () {
    $this->clinic->cart()->create();

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJsonStructure([
            'subtotal',
            'total',
            'itemsCount',
            'uniqueItemsCount',
        ]);
});

it('calculates the subtotal correctly', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 11111,
        'quantity' => 2,
    ]);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart')
        ->assertOk()
        ->assertJson(['subtotal' => '222.22']);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart-summary")
        ->assertOk()
        ->assertJson(['subtotal' => '222.22']);
});

it('calculates the total number of vendors correctly', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 11111,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJsonCount(1, 'vendors');
});

it('calculates the subtotal per vendor correctly', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 11111,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'subtotal' => '222.22',
                ],
            ],
        ]);
});

it('calculates the shipping fee correctly', function (int $productPrice, string $expectedShippingFee, string $expectedAmountToFreeShipping) {
    $this->vendor->shippingTerms()->create([
        'cutoff_time' => null,
        'shipping_rate' => 1000,
        'free_shipping_threshold' => 20000,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $productPrice,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'shippingFee' => $expectedShippingFee,
                    'amountToFreeShipping' => $expectedAmountToFreeShipping,
                ],
            ],
        ]);
})->with([
    [10000, '10.00', '100.00'],
    [19999, '10.00', '0.01'],
    [20000, '0.00', '0.00'],
]);

it('calculates the shipping fee for a vendor with no shipping terms', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 10000,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'shippingFee' => '0.00',
                    'amountToFreeShipping' => '0.00',
                ],
            ],
        ]);
});

it('calculates the shipping fee for a vendor with no free shipping threshold', function () {
    $this->vendor->shippingTerms()->create([
        'cutoff_time' => null,
        'shipping_rate' => 1000,
        'free_shipping_threshold' => 0,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 10000,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'shippingFee' => '10.00',
                    'amountToFreeShipping' => '0.00',
                ],
            ],
        ]);
});

it('calculates the cutoff time correctly', function (string $cutoffTime) {
    $this->travelTo(Carbon::parse('2024-01-01'));

    $this->vendor->shippingTerms()->create([
        'cutoff_time' => $cutoffTime,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 10000,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'cutoffTime' => $cutoffTime,
                ],
            ],
        ]);
})->with([
    '2PM PST',
    '12PM EST',
    '12:30PM Local Time',
]);

it('returns the correct budget type', function () {
    $this->clinic->budgetSettings()->create([
        'type' => ClinicBudgetType::Static,
        'weekly_cogs' => 100000,
        'weekly_ga' => 20000,
        'monthly_cogs' => 400000,
        'monthly_ga' => 80000,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'budget' => [
                'type' => ClinicBudgetType::Static->value,
                'weekToDate' => [
                    [
                        'category' => 'COGS',
                        'spent' => 0,
                        'target' => 1000,
                    ],
                    [
                        'category' => 'GA',
                        'spent' => 0,
                        'target' => 200.00,
                    ],
                ],
                'monthToDate' => [
                    [
                        'category' => 'COGS',
                        'spent' => 0,
                        'target' => 4000,
                    ],
                    [
                        'category' => 'GA',
                        'spent' => 0,
                        'target' => 800,
                    ],
                ],
            ],
        ]);
});

it('returns the correct prices', function () {
    $product = Product::factory()->create();

    $this->productOffer->update(['product_id' => $product->id]);

    $this->clinic->productOffers()->syncWithoutDetaching([
        $this->productOffer->id => ['price' => 12345],
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 23456,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertJson([
        'vendors' => [
            [
                'items' => [
                    [
                        'price' => '234.56',
                        'product' => [
                            'offers' => [
                                [
                                    'price' => Money::ofMinor($this->productOffer->price, 'USD')->getAmount(),
                                    'clinicPrice' => '123.45',
                                ],
                            ],
                        ],

                    ],
                ],
            ],
        ],
    ]);
});

it('calculates the total correctly', function () {
    $this->vendor->shippingTerms()->create([
        'cutoff_time' => null,
        'shipping_rate' => 1000,
        'free_shipping_threshold' => 20000,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 9990,
        'quantity' => 2,
    ]);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart')
        ->assertOk()
        ->assertJson(['total' => '209.80']);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart-summary")
        ->assertOk()
        ->assertJson(['total' => '209.80']);
});

it('calculates the items count correctly', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 9990,
        'quantity' => 2,
    ]);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart')
        ->assertOk()
        ->assertJson([
            'itemsCount' => 2,
            'uniqueItemsCount' => 1,
        ]);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart-summary")
        ->assertOk()
        ->assertJson([
            'itemsCount' => 2,
            'uniqueItemsCount' => 1,
        ]);
});

it('returns the correct last ordered date and quantity', function () {
    $this->travelTo(Carbon::parse('2024-01-01'), function () {
        $order = Order::factory()->for($this->clinic)->create();
        $order->items()->create([
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 2,
            'price' => 9990,
            'status' => OrderItemStatus::Pending,
        ]);
    });

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 9990,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'items' => [
                        [
                            'product' => [
                                'offers' => [
                                    [
                                        'lastOrderedAt' => '2024-01-01T00:00:00.000000Z',
                                        'lastOrderedQuantity' => 2,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
});

it('calculates the spent percentage correctly', function () {
    $this->clinic->budgetSettings()->create([
        'type' => ClinicBudgetType::Static,
        'weekly_cogs' => 10000,
        'weekly_ga' => 2000,
    ]);

    $this->vendor->update(['expense_category' => ExpenseCategory::COGS]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 9990,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'budget' => [
                'weekToDate' => [
                    [
                        'category' => 'COGS',
                        'spent' => '200',
                        'spentPercentage' => 200,
                        'target' => '100.00',
                        'targetPercentage' => 0,
                    ],
                ],
            ],
        ]);
});

it('returns the correct offers', function () {
    $product = Product::factory()->create();

    $this->productOffer->update(['product_id' => $product->id]);

    $vendorB = Vendor::factory()->enabled()->create();
    $productB = ProductOffer::factory()->for($vendorB)->create([
        'product_id' => $product->id,
        'price' => 1234,
    ]);
    $productB->clinics()->attach($this->clinic->id, ['price' => 1234]);

    $vendorC = Vendor::factory()->enabled()->create();
    $productC = ProductOffer::factory()->for($vendorC)->create([
        'product_id' => $product->id,
        'price' => 3990,
    ]);

    IntegrationConnection::create([
        'vendor_id' => $vendorB->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'quantity' => 1,
        'price' => 9990,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson('/api/cart');

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'items' => [
                        [
                            'productOfferId' => $this->productOffer->id,
                        ],
                    ],
                ],
            ],
        ])
        ->assertJsonMissingExact([
            'id' => $productC->id,
            'vendorName' => $productC->vendor->name,
            'price' => '39.90',
        ]);
});
